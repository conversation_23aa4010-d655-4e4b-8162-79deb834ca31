function BowelSoundLabeler()
    % 肠鸣音标注器 - MATLAB GUI应用程序
    % 功能：对音频/信号文件进行肠鸣音标注并自动重命名
    % 作者：自动生成
    % 日期：2025-08-21
    
    % 创建主窗口
    fig = uifigure('Name', '肠鸣音标注器', 'Position', [100, 100, 600, 400]);
    fig.Resize = 'off';
    
    % 创建网格布局
    grid = uigridlayout(fig, [6, 2]);
    grid.RowHeight = {'fit', 'fit', 'fit', '1x', 'fit', 'fit'};
    grid.ColumnWidth = {'1x', '1x'};
    
    % 标题
    titleLabel = uilabel(grid, 'Text', '肠鸣音标注器', 'FontSize', 18, 'FontWeight', 'bold');
    titleLabel.Layout.Row = 1;
    titleLabel.Layout.Column = [1, 2];
    titleLabel.HorizontalAlignment = 'center';
    
    % 说明文字
    infoLabel = uilabel(grid, 'Text', '选择音频/信号文件进行肠鸣音标注，系统将自动重命名文件');
    infoLabel.Layout.Row = 2;
    infoLabel.Layout.Column = [1, 2];
    infoLabel.HorizontalAlignment = 'center';
    
    % 按钮区域
    selectSingleBtn = uibutton(grid, 'Text', '选择单个文件', 'ButtonPushedFcn', @(btn,event) selectSingleFile());
    selectSingleBtn.Layout.Row = 3;
    selectSingleBtn.Layout.Column = 1;
    
    selectMultipleBtn = uibutton(grid, 'Text', '选择多个文件', 'ButtonPushedFcn', @(btn,event) selectMultipleFiles());
    selectMultipleBtn.Layout.Row = 3;
    selectMultipleBtn.Layout.Column = 2;
    
    % 文件列表显示区域
    fileListArea = uitextarea(grid, 'Value', {'选择文件后将在此显示文件列表...'}, 'Editable', 'off');
    fileListArea.Layout.Row = 4;
    fileListArea.Layout.Column = [1, 2];
    
    % 开始标注按钮
    startLabelBtn = uibutton(grid, 'Text', '开始标注', 'ButtonPushedFcn', @(btn,event) startLabeling(), 'Enable', 'off');
    startLabelBtn.Layout.Row = 5;
    startLabelBtn.Layout.Column = [1, 2];
    startLabelBtn.BackgroundColor = [0.2, 0.7, 0.2];
    startLabelBtn.FontColor = 'white';
    startLabelBtn.FontWeight = 'bold';
    
    % 状态栏
    statusLabel = uilabel(grid, 'Text', '就绪', 'FontColor', [0.5, 0.5, 0.5]);
    statusLabel.Layout.Row = 6;
    statusLabel.Layout.Column = [1, 2];
    statusLabel.HorizontalAlignment = 'center';
    
    % 全局变量存储选中的文件
    selectedFiles = {};
    
    % 选择单个文件函数
    function selectSingleFile()
        try
            [filename, pathname] = uigetfile({'*.mat;*.wav;*.mp3;*.m4a;*.flac', '音频/信号文件 (*.mat,*.wav,*.mp3,*.m4a,*.flac)'; ...
                                            '*.mat', 'MATLAB文件 (*.mat)'; ...
                                            '*.wav', 'WAV文件 (*.wav)'; ...
                                            '*.mp3', 'MP3文件 (*.mp3)'; ...
                                            '*.m4a', 'M4A文件 (*.m4a)'; ...
                                            '*.flac', 'FLAC文件 (*.flac)'; ...
                                            '*.*', '所有文件 (*.*)'}, ...
                                            '选择音频/信号文件');
            
            if filename ~= 0
                selectedFiles = {fullfile(pathname, filename)};
                updateFileList();
                startLabelBtn.Enable = 'on';
                statusLabel.Text = '已选择1个文件';
            end
        catch ME
            uialert(fig, ['选择文件时出错: ' ME.message], '错误');
        end
    end
    
    % 选择多个文件函数
    function selectMultipleFiles()
        try
            [filenames, pathname] = uigetfile({'*.mat;*.wav;*.mp3;*.m4a;*.flac', '音频/信号文件 (*.mat,*.wav,*.mp3,*.m4a,*.flac)'; ...
                                             '*.mat', 'MATLAB文件 (*.mat)'; ...
                                             '*.wav', 'WAV文件 (*.wav)'; ...
                                             '*.mp3', 'MP3文件 (*.mp3)'; ...
                                             '*.m4a', 'M4A文件 (*.m4a)'; ...
                                             '*.flac', 'FLAC文件 (*.flac)'; ...
                                             '*.*', '所有文件 (*.*)'}, ...
                                             '选择音频/信号文件', 'MultiSelect', 'on');
            
            if ~isequal(filenames, 0)
                if ischar(filenames)
                    filenames = {filenames};
                end
                selectedFiles = cellfun(@(x) fullfile(pathname, x), filenames, 'UniformOutput', false);
                updateFileList();
                startLabelBtn.Enable = 'on';
                statusLabel.Text = sprintf('已选择%d个文件', length(selectedFiles));
            end
        catch ME
            uialert(fig, ['选择文件时出错: ' ME.message], '错误');
        end
    end
    
    % 更新文件列表显示
    function updateFileList()
        if isempty(selectedFiles)
            fileListArea.Value = {'选择文件后将在此显示文件列表...'};
        else
            fileList = cell(length(selectedFiles), 1);
            for i = 1:length(selectedFiles)
                [~, name, ext] = fileparts(selectedFiles{i});
                fileList{i} = sprintf('%d. %s%s', i, name, ext);
            end
            fileListArea.Value = fileList;
        end
    end
    
    % 开始标注函数
    function startLabeling()
        if isempty(selectedFiles)
            uialert(fig, '请先选择文件', '提示');
            return;
        end
        
        % 禁用按钮防止重复操作
        startLabelBtn.Enable = 'off';
        selectSingleBtn.Enable = 'off';
        selectMultipleBtn.Enable = 'off';
        
        processedCount = 0;
        skippedCount = 0;
        errorCount = 0;
        
        try
            for i = 1:length(selectedFiles)
                currentFile = selectedFiles{i};
                [filepath, filename, ext] = fileparts(currentFile);
                
                % 更新状态
                statusLabel.Text = sprintf('正在处理文件 %d/%d: %s%s', i, length(selectedFiles), filename, ext);
                drawnow;
                
                % 询问是否包含肠鸣音
                answer = questdlg(sprintf('文件: %s%s\n\n此文件是否包含肠鸣音？', filename, ext), ...
                                '肠鸣音标注', '是', '否', '跳过', '否');
                
                if strcmp(answer, '跳过') || isempty(answer)
                    skippedCount = skippedCount + 1;
                    continue;
                end
                
                try
                    if strcmp(answer, '否')
                        % 无肠鸣音，添加_no后缀
                        newFilename = [filename '_no' ext];
                        newFilePath = fullfile(filepath, newFilename);
                        
                        % 检查文件是否已存在
                        if exist(newFilePath, 'file')
                            choice = questdlg(sprintf('文件 %s 已存在，是否覆盖？', newFilename), ...
                                            '文件已存在', '覆盖', '跳过', '跳过');
                            if ~strcmp(choice, '覆盖')
                                skippedCount = skippedCount + 1;
                                continue;
                            end
                        end
                        
                        % 重命名文件
                        movefile(currentFile, newFilePath);
                        processedCount = processedCount + 1;
                        
                    elseif strcmp(answer, '是')
                        % 有肠鸣音，询问数量
                        countStr = inputdlg('请输入肠鸣音的数量:', '肠鸣音数量', 1, {'1'});
                        
                        if isempty(countStr) || isempty(countStr{1})
                            skippedCount = skippedCount + 1;
                            continue;
                        end
                        
                        count = str2double(countStr{1});
                        if isnan(count) || count < 0 || count ~= round(count)
                            uialert(fig, '请输入有效的非负整数', '输入错误');
                            skippedCount = skippedCount + 1;
                            continue;
                        end
                        
                        % 添加_yes_N后缀
                        newFilename = sprintf('%s_yes_%d%s', filename, count, ext);
                        newFilePath = fullfile(filepath, newFilename);
                        
                        % 检查文件是否已存在
                        if exist(newFilePath, 'file')
                            choice = questdlg(sprintf('文件 %s 已存在，是否覆盖？', newFilename), ...
                                            '文件已存在', '覆盖', '跳过', '跳过');
                            if ~strcmp(choice, '覆盖')
                                skippedCount = skippedCount + 1;
                                continue;
                            end
                        end
                        
                        % 重命名文件
                        movefile(currentFile, newFilePath);
                        processedCount = processedCount + 1;
                    end
                    
                catch ME
                    errorCount = errorCount + 1;
                    uialert(fig, sprintf('处理文件 %s%s 时出错: %s', filename, ext, ME.message), '错误');
                end
            end
            
            % 显示完成信息
            resultMsg = sprintf('标注完成！\n\n处理成功: %d 个文件\n跳过: %d 个文件\n错误: %d 个文件', ...
                              processedCount, skippedCount, errorCount);
            uialert(fig, resultMsg, '完成', 'Icon', 'success');
            
        catch ME
            uialert(fig, ['标注过程中出现错误: ' ME.message], '错误');
        end
        
        % 重新启用按钮
        startLabelBtn.Enable = 'on';
        selectSingleBtn.Enable = 'on';
        selectMultipleBtn.Enable = 'on';
        
        % 清空文件列表
        selectedFiles = {};
        updateFileList();
        startLabelBtn.Enable = 'off';
        statusLabel.Text = '标注完成，就绪';
    end
end
