function segmentAndSaveTimeTable(tt1, tt2, originalFileName, outputFolder, config)
%SEGMENTANDSAVETIMETABLE 二次分割时间表并保存为小片段（支持双时间刻度）
%   将降噪后的长时间表分割为更小的时间片段，适用于精细标注任务。
%   该函数实现了两阶段处理策略：先进行长时间窗口降噪，再分割为小片段。
%   支持双时间刻度保存：原始连续时间刻度和重置时间刻度。
%
%   语法:
%   segmentAndSaveTimeTable(tt1, tt2, originalFileName, outputFolder, config)
%
%   输入参数:
%   tt1              - 第一通道的时间表数据
%   tt2              - 第二通道的时间表数据
%   originalFileName - 原始文件名（用于生成输出文件名）
%   outputFolder     - 输出文件夹路径
%   config          - 处理配置参数结构体
%
%   输出结果:
%   根据配置在指定文件夹的子文件夹中生成多个小片段的MAT文件：
%   - original_timeline/: 保持原始连续时间刻度的分割文件
%   - reset_timeline/: 每个文件从0开始重新计时的分割文件
%   文件命名格式: [原文件名]_seg[序号]_tt.mat
%   每个文件包含: [原文件名]_seg[序号]_tt1, [原文件名]_seg[序号]_tt2
%
%   配置参数:
%   config.secondarySegmentLength - 二次分割长度（秒）
%   config.secondaryOverlapRatio  - 重叠比例（0-0.5）
%   config.minSegmentLength      - 最小片段长度（秒）
%   config.samplingRate          - 采样率（Hz）
%   config.enableDualTimeScale   - 启用双时间刻度保存
%   config.originalTimelineFolder - 原始时间刻度文件夹名
%   config.resetTimelineFolder   - 重置时间刻度文件夹名
%   config.saveOriginalTimeline  - 保存原始连续时间刻度
%   config.saveResetTimeline     - 保存重置时间刻度
%
%   处理流程:
%   1. 计算分割参数（片段长度、重叠长度等）
%   2. 按指定长度和重叠比例分割时间表
%   3. 处理边界条件和最后一个片段
%   4. 为每个片段生成唯一的文件名和变量名
%   5. 保存所有片段到独立的MAT文件
%
%   优势特点:
%   - 保持降噪算法的长时间窗口优势
%   - 提供适合标注的小时间尺度
%   - 支持可配置的重叠处理
%   - 自动处理边界条件
%   - 生成标准化的文件命名
%
%   示例:
%   % 基本用法
%   config = createProcessingConfig();
%   config.secondarySegmentLength = 10;  % 10秒片段
%   config.secondaryOverlapRatio = 0.1;  % 10%重叠
%   segmentAndSaveTimeTable(tt1, tt2, 'data1_5min.csv', './output', config);
%
%   % 生成文件示例:
%   % data1_5min_seg001_tt.mat (包含: data1_5min_seg001_tt1, data1_5min_seg001_tt2)
%   % data1_5min_seg002_tt.mat (包含: data1_5min_seg002_tt1, data1_5min_seg002_tt2)
%   % ...
%
%   注意事项:
%   - 重叠比例应在0-0.5之间，避免过度重叠
%   - 最后一个片段如果长度不足minSegmentLength会被丢弃
%   - 文件名中的特殊字符会被自动清理
%   - 确保输出文件夹有足够的磁盘空间
%
%   参见: CREATEPROCESSINGCONFIG, CSV_LVBO_TO_MAT_TT
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 参数验证和初始化
    if nargin < 5
        error('segmentAndSaveTimeTable:NotEnoughInputs', '需要5个输入参数');
    end
    
    % 验证时间表数据
    if height(tt1) ~= height(tt2)
        error('segmentAndSaveTimeTable:DimensionMismatch', '两个通道的时间表长度不匹配');
    end
    
    % 获取基本参数
    totalSamples = height(tt1);
    samplingRate = config.samplingRate;
    totalDuration = totalSamples / samplingRate;  % 总时长（秒）
    
    % 计算分割参数
    segmentLengthSamples = round(config.secondarySegmentLength * samplingRate);
    overlapSamples = round(segmentLengthSamples * config.secondaryOverlapRatio);
    stepSamples = segmentLengthSamples - overlapSamples;
    minSegmentSamples = round(config.minSegmentLength * samplingRate);
    
    % 计算总的片段数量（改进算法，确保覆盖所有数据）
    if totalSamples <= segmentLengthSamples
        numSegments = 1;  % 如果总长度小于等于片段长度，只有一个片段
    else
        % 计算完整片段数
        numCompleteSegments = floor((totalSamples - segmentLengthSamples) / stepSamples) + 1;
        % 检查是否还有剩余数据需要额外的片段
        lastSegmentStart = (numCompleteSegments - 1) * stepSamples + 1;
        remainingSamples = totalSamples - lastSegmentStart + 1;

        if remainingSamples > segmentLengthSamples
            numSegments = numCompleteSegments + 1;  % 需要额外的片段
        else
            numSegments = numCompleteSegments;
        end
    end
    
    % 生成基础文件名
    [~, baseName, ~] = fileparts(originalFileName);
    cleanBaseName = regexprep(baseName, '[^a-zA-Z0-9_]', '_');
    if ~isempty(cleanBaseName) && ~isletter(cleanBaseName(1))
        cleanBaseName = ['file_', cleanBaseName];
    end

    %% 创建双时间刻度文件夹
    originalTimelineFolder = '';
    resetTimelineFolder = '';

    if config.enableDualTimeScale
        if config.saveOriginalTimeline
            originalTimelineFolder = fullfile(outputFolder, config.originalTimelineFolder);
            if ~exist(originalTimelineFolder, 'dir')
                mkdir(originalTimelineFolder);
                if config.enableVerboseOutput
                    fprintf('创建原始时间刻度文件夹: %s\n', originalTimelineFolder);
                end
            end
        end

        if config.saveResetTimeline
            resetTimelineFolder = fullfile(outputFolder, config.resetTimelineFolder);
            if ~exist(resetTimelineFolder, 'dir')
                mkdir(resetTimelineFolder);
                if config.enableVerboseOutput
                    fprintf('创建重置时间刻度文件夹: %s\n', resetTimelineFolder);
                end
            end
        end
    end
    
    %% 显示分割信息
    if config.enableVerboseOutput
        fprintf('=== 二次分割信息 ===\n');
        fprintf('原始时长: %.2f 秒 (%d 样本)\n', totalDuration, totalSamples);
        fprintf('片段长度: %.2f 秒 (%d 样本)\n', config.secondarySegmentLength, segmentLengthSamples);
        fprintf('重叠长度: %.2f 秒 (%d 样本)\n', overlapSamples/samplingRate, overlapSamples);
        fprintf('步进长度: %.2f 秒 (%d 样本)\n', stepSamples/samplingRate, stepSamples);
        fprintf('最小片段长度: %.2f 秒 (%d 样本)\n', config.minSegmentLength, minSegmentSamples);
        fprintf('预计片段数: %d\n', numSegments);
        fprintf('==================\n');
    end
    
    %% 执行分割和保存
    savedCount = 0;
    
    for segIdx = 1:numSegments
        % 计算当前片段的起始和结束位置
        startIdx = (segIdx - 1) * stepSamples + 1;
        endIdx = min(startIdx + segmentLengthSamples - 1, totalSamples);

        % 检查片段长度是否满足最小要求
        currentSegmentLength = endIdx - startIdx + 1;

        if config.enableVerboseOutput
            fprintf('片段 %d: 起始=%d, 结束=%d, 长度=%d样本(%.2f秒)\n', ...
                segIdx, startIdx, endIdx, currentSegmentLength, currentSegmentLength/samplingRate);
        end

        if currentSegmentLength < minSegmentSamples
            if config.enableVerboseOutput
                fprintf('  -> 片段 %d 长度不足 (%.2f秒 < %.2f秒)，跳过保存\n', ...
                    segIdx, currentSegmentLength/samplingRate, config.minSegmentLength);
            end
            continue;
        end
        
        % 提取当前片段的数据（原始时间刻度）
        segmentTT1_original = tt1(startIdx:endIdx, :);
        segmentTT2_original = tt2(startIdx:endIdx, :);

        % 生成片段文件名和变量名
        segmentSuffix = sprintf('_seg%03d', segIdx);
        var1Name = [cleanBaseName, segmentSuffix, '_tt1'];
        var2Name = [cleanBaseName, segmentSuffix, '_tt2'];

        % 保存数据到不同的时间刻度文件夹
        segmentSaved = false;

        try
            % 保存原始时间刻度版本
            if config.enableDualTimeScale && config.saveOriginalTimeline
                originalFileName = fullfile(originalTimelineFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);

                eval([var1Name, ' = segmentTT1_original;']);
                eval([var2Name, ' = segmentTT2_original;']);
                save(originalFileName, var1Name, var2Name);

                if config.enableProgressDisplay
                    fprintf('已保存原始时间刻度片段 %d: %s (%.2f-%.2f秒)\n', ...
                        segIdx, [cleanBaseName, segmentSuffix, '_tt.mat'], ...
                        (startIdx-1)/samplingRate, (endIdx-1)/samplingRate);
                end
                segmentSaved = true;
            end

            % 保存重置时间刻度版本
            if config.enableDualTimeScale && config.saveResetTimeline
                resetFileName = fullfile(resetTimelineFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);

                % 创建重置时间刻度的时间表
                segmentTT1_reset = createResetTimeTable(segmentTT1_original, config.samplingRate);
                segmentTT2_reset = createResetTimeTable(segmentTT2_original, config.samplingRate);

                eval([var1Name, ' = segmentTT1_reset;']);
                eval([var2Name, ' = segmentTT2_reset;']);
                save(resetFileName, var1Name, var2Name);

                if config.enableProgressDisplay
                    fprintf('已保存重置时间刻度片段 %d: %s (0-%.2f秒)\n', ...
                        segIdx, [cleanBaseName, segmentSuffix, '_tt.mat'], ...
                        config.secondarySegmentLength);
                end
                segmentSaved = true;
            end

            % 如果没有启用双时间刻度，使用原有逻辑
            if ~config.enableDualTimeScale
                segmentFileName = fullfile(outputFolder, [cleanBaseName, segmentSuffix, '_tt.mat']);
                eval([var1Name, ' = segmentTT1_original;']);
                eval([var2Name, ' = segmentTT2_original;']);
                save(segmentFileName, var1Name, var2Name);
                segmentSaved = true;

                if config.enableProgressDisplay
                    fprintf('已保存片段 %d/%d: %s (%.2f-%.2f秒)\n', ...
                        segIdx, numSegments, [cleanBaseName, segmentSuffix, '_tt.mat'], ...
                        (startIdx-1)/samplingRate, (endIdx-1)/samplingRate);
                end
            end

            if segmentSaved
                savedCount = savedCount + 1;
            end

        catch ME
            if config.enableErrorHandling
                warning('segmentAndSaveTimeTable:SaveFailed', ...
                    '片段 %d 保存失败: %s', segIdx, ME.message);
            else
                rethrow(ME);
            end
        end
    end
    
    %% 输出总结信息
    if config.enableVerboseOutput
        fprintf('\n=== 二次分割完成 ===\n');
        fprintf('成功保存片段数: %d/%d\n', savedCount, numSegments);
        fprintf('平均片段长度: %.2f 秒\n', config.secondarySegmentLength);

        if config.enableDualTimeScale
            if config.saveOriginalTimeline
                fprintf('原始时间刻度文件夹: %s\n', originalTimelineFolder);
            end
            if config.saveResetTimeline
                fprintf('重置时间刻度文件夹: %s\n', resetTimelineFolder);
            end
        else
            fprintf('输出文件夹: %s\n', outputFolder);
        end
        fprintf('===================\n\n');
    end
end

function resetTT = createResetTimeTable(originalTT, samplingRate)
%CREATERESETTIMETABLE 创建重置时间刻度的时间表
%   将原始时间表的时间刻度重置为从0开始
%
%   输入参数:
%   originalTT   - 原始时间表
%   samplingRate - 采样率
%
%   输出参数:
%   resetTT      - 重置时间刻度的时间表

    % 获取原始数据
    data = originalTT.Variables;
    numSamples = height(originalTT);

    % 创建从0开始的新时间表
    resetTT = timetable(data, 'SampleRate', samplingRate);
end
